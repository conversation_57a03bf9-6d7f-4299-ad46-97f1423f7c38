/* ===== QR CODE SCANNER SYSTEM ===== */

class QRCodeScanner {
    constructor() {
        this.scanner = null;
        this.isScanning = false;
        this.videoElement = null;
        this.canvasElement = null;
        this.resultElement = null;
        this.stream = null;

        this.init();
    }

    init() {
        console.log('🔍 QR Scanner initialized');
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Bouton pour démarrer le scan
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-qr-scan]')) {
                e.preventDefault();
                this.startScanning();
            }

            if (e.target.matches('[data-qr-stop]')) {
                e.preventDefault();
                this.stopScanning();
            }

            if (e.target.matches('[data-qr-generate]')) {
                e.preventDefault();
                this.generateQRCode(e.target.dataset.qrGenerate);
            }
        });
    }

    async startScanning() {
        try {
            console.log('📷 Starting QR scan...');

            // Vérifier la disponibilité de la caméra
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                this.showPermissionError('Votre navigateur ne supporte pas l\'accès à la caméra.');
                return;
            }

            // Créer l'interface de scan
            this.createScanInterface();

            // Vérifier les permissions avant de demander l'accès
            await this.checkCameraPermissions();

            // Demander l'accès à la caméra avec gestion d'erreurs détaillée
            await this.requestCameraAccess();

            this.isScanning = true;
            this.scanFrame();

            // Afficher l'interface
            document.getElementById('qr-scanner-modal').style.display = 'block';

        } catch (error) {
            console.error('❌ Error starting camera:', error);
            this.handleCameraError(error);
        }
    }

    async checkCameraPermissions() {
        try {
            // Vérifier les permissions avec l'API Permissions si disponible
            if (navigator.permissions) {
                const permission = await navigator.permissions.query({ name: 'camera' });
                console.log('📷 Camera permission status:', permission.state);

                if (permission.state === 'denied') {
                    this.showPermissionError('L\'accès à la caméra a été refusé. Veuillez autoriser l\'accès dans les paramètres de votre navigateur.');
                    return false;
                }
            }
            return true;
        } catch (error) {
            console.log('⚠️ Permission API not available, proceeding with camera request');
            return true;
        }
    }

    async requestCameraAccess() {
        // Afficher un message de demande de permission
        this.showPermissionRequest();

        try {
            // Essayer d'abord avec la caméra arrière
            this.stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'environment',
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                }
            });

            console.log('✅ Camera access granted (rear camera)');

        } catch (error) {
            console.log('⚠️ Rear camera failed, trying front camera...');

            try {
                // Si la caméra arrière échoue, essayer la caméra avant
                this.stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'user',
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });

                console.log('✅ Camera access granted (front camera)');

            } catch (frontError) {
                console.log('⚠️ Front camera failed, trying any available camera...');

                // En dernier recours, essayer n'importe quelle caméra
                this.stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });

                console.log('✅ Camera access granted (any camera)');
            }
        }

        // Configurer la vidéo
        this.videoElement.srcObject = this.stream;
        this.videoElement.play();

        // Masquer le message de permission
        this.hidePermissionRequest();
    }

    showPermissionRequest() {
        const permissionHTML = `
            <div id="camera-permission-request" class="camera-permission-overlay">
                <div class="permission-content">
                    <div class="permission-icon">
                        <i class="fas fa-camera fa-3x text-primary"></i>
                    </div>
                    <h4>Autorisation caméra requise</h4>
                    <p>Pour scanner les QR codes, nous avons besoin d'accéder à votre caméra.</p>
                    <div class="permission-steps">
                        <div class="step">
                            <span class="step-number">1</span>
                            <span>Cliquez "Autoriser" dans la popup du navigateur</span>
                        </div>
                        <div class="step">
                            <span class="step-number">2</span>
                            <span>Si aucune popup n'apparaît, vérifiez l'icône caméra dans la barre d'adresse</span>
                        </div>
                    </div>
                    <div class="permission-loading">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        En attente de votre autorisation...
                    </div>
                </div>
            </div>
        `;

        const scannerBody = document.querySelector('.qr-scanner-body');
        if (scannerBody) {
            scannerBody.insertAdjacentHTML('afterbegin', permissionHTML);
        }
    }

    hidePermissionRequest() {
        const permissionOverlay = document.getElementById('camera-permission-request');
        if (permissionOverlay) {
            permissionOverlay.remove();
        }
    }

    showPermissionError(message) {
        const errorHTML = `
            <div id="camera-permission-error" class="camera-permission-overlay">
                <div class="permission-content">
                    <div class="permission-icon">
                        <i class="fas fa-exclamation-triangle fa-3x text-danger"></i>
                    </div>
                    <h4>Accès caméra refusé</h4>
                    <p>${message}</p>
                    <div class="permission-help">
                        <h6>Comment autoriser l'accès :</h6>
                        <ul>
                            <li><strong>Chrome/Edge :</strong> Cliquez sur l'icône caméra dans la barre d'adresse</li>
                            <li><strong>Firefox :</strong> Cliquez sur l'icône bouclier ou caméra</li>
                            <li><strong>Safari :</strong> Safari → Préférences → Sites web → Caméra</li>
                            <li><strong>Mobile :</strong> Paramètres → Apps → Navigateur → Permissions</li>
                        </ul>
                    </div>
                    <div class="permission-actions">
                        <button class="btn btn-primary" onclick="qrScanner.retryCamera()">
                            <i class="fas fa-redo me-2"></i>Réessayer
                        </button>
                        <button class="btn btn-secondary" onclick="qrScanner.stopScanning()">
                            <i class="fas fa-times me-2"></i>Annuler
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Créer une interface minimale si elle n'existe pas
        if (!document.getElementById('qr-scanner-modal')) {
            this.createScanInterface();
        }

        const scannerBody = document.querySelector('.qr-scanner-body');
        if (scannerBody) {
            scannerBody.innerHTML = errorHTML;
        }

        // Afficher la modal
        document.getElementById('qr-scanner-modal').style.display = 'block';
    }

    handleCameraError(error) {
        let errorMessage = 'Erreur inconnue lors de l\'accès à la caméra.';

        switch (error.name) {
            case 'NotAllowedError':
                errorMessage = 'L\'accès à la caméra a été refusé. Veuillez autoriser l\'accès et réessayer.';
                break;
            case 'NotFoundError':
                errorMessage = 'Aucune caméra trouvée sur cet appareil.';
                break;
            case 'NotReadableError':
                errorMessage = 'La caméra est déjà utilisée par une autre application.';
                break;
            case 'OverconstrainedError':
                errorMessage = 'Les paramètres de caméra demandés ne sont pas supportés.';
                break;
            case 'SecurityError':
                errorMessage = 'Accès caméra bloqué pour des raisons de sécurité. Utilisez HTTPS.';
                break;
            case 'AbortError':
                errorMessage = 'L\'accès à la caméra a été interrompu.';
                break;
        }

        console.error('📷 Camera error details:', {
            name: error.name,
            message: error.message,
            constraint: error.constraint
        });

        this.showPermissionError(errorMessage);
    }

    async retryCamera() {
        console.log('🔄 Retrying camera access...');

        // Nettoyer l'interface d'erreur
        const errorOverlay = document.getElementById('camera-permission-error');
        if (errorOverlay) {
            errorOverlay.remove();
        }

        // Réessayer l'accès caméra
        try {
            await this.requestCameraAccess();
            this.isScanning = true;
            this.scanFrame();
        } catch (error) {
            this.handleCameraError(error);
        }
    }

    createScanInterface() {
        // Supprimer l'interface existante si elle existe
        const existingModal = document.getElementById('qr-scanner-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // Créer l'interface de scan
        const modalHTML = `
            <div id="qr-scanner-modal" class="qr-scanner-modal">
                <div class="qr-scanner-content">
                    <div class="qr-scanner-header">
                        <h3><i class="fas fa-qrcode me-2"></i>Scanner QR Code</h3>
                        <button class="qr-close-btn" data-qr-stop>
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="qr-scanner-body">
                        <div class="camera-container">
                            <video id="qr-video" autoplay muted playsinline></video>
                            <canvas id="qr-canvas" style="display: none;"></canvas>

                            <!-- Overlay de scan -->
                            <div class="scan-overlay">
                                <div class="scan-frame">
                                    <div class="scan-corners">
                                        <div class="corner top-left"></div>
                                        <div class="corner top-right"></div>
                                        <div class="corner bottom-left"></div>
                                        <div class="corner bottom-right"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Instructions -->
                            <div class="scan-instructions">
                                <p><i class="fas fa-camera me-2"></i>Placez le QR code dans le cadre</p>
                                <div class="scan-status" id="scan-status">
                                    <i class="fas fa-search me-2"></i>Recherche en cours...
                                </div>
                            </div>
                        </div>

                        <!-- Résultat -->
                        <div id="qr-result" class="qr-result" style="display: none;">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <span id="qr-result-text"></span>
                            </div>
                        </div>

                        <!-- Erreur -->
                        <div id="qr-error" class="qr-error" style="display: none;">
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <span id="qr-error-text"></span>
                            </div>
                        </div>
                    </div>

                    <div class="qr-scanner-footer">
                        <button class="btn btn-secondary" data-qr-stop>
                            <i class="fas fa-times me-2"></i>Annuler
                        </button>
                        <button class="btn btn-primary" onclick="qrScanner.switchCamera()">
                            <i class="fas fa-sync-alt me-2"></i>Changer caméra
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);

        this.videoElement = document.getElementById('qr-video');
        this.canvasElement = document.getElementById('qr-canvas');
        this.resultElement = document.getElementById('qr-result');
    }

    scanFrame() {
        if (!this.isScanning) return;

        try {
            if (this.videoElement.readyState === this.videoElement.HAVE_ENOUGH_DATA) {
                // Configurer le canvas
                this.canvasElement.width = this.videoElement.videoWidth;
                this.canvasElement.height = this.videoElement.videoHeight;

                const context = this.canvasElement.getContext('2d');
                context.drawImage(this.videoElement, 0, 0);

                // Obtenir les données d'image
                const imageData = context.getImageData(0, 0, this.canvasElement.width, this.canvasElement.height);

                // Scanner avec jsQR
                const code = jsQR(imageData.data, imageData.width, imageData.height);

                if (code) {
                    console.log('✅ QR Code detected:', code.data);
                    this.handleQRResult(code.data);
                    return;
                }
            }
        } catch (error) {
            console.error('❌ Scan error:', error);
        }

        // Continuer le scan
        requestAnimationFrame(() => this.scanFrame());
    }

    handleQRResult(data) {
        console.log('🎯 QR Result:', data);

        // Arrêter le scan
        this.isScanning = false;

        // Afficher le résultat
        document.getElementById('qr-result-text').textContent = data;
        document.getElementById('qr-result').style.display = 'block';
        document.getElementById('scan-status').style.display = 'none';

        // Traiter selon le type de QR code
        if (data.startsWith('LOCASPACE_AUTH:')) {
            this.handleAuthQR(data);
        } else if (data.startsWith('LOCASPACE_RESERVATION:')) {
            this.handleReservationQR(data);
        } else if (data.includes('/auth/qr-login/')) {
            this.handleAuthURLQR(data);
        } else if (data.startsWith('http')) {
            this.handleURLQR(data);
        } else {
            this.handleGenericQR(data);
        }

        // Fermer automatiquement après 2 secondes pour les authentifications
        if (data.startsWith('LOCASPACE_AUTH:') || data.includes('/auth/qr-login/')) {
            setTimeout(() => {
                this.stopScanning();
            }, 2000);
        } else {
            setTimeout(() => {
                this.stopScanning();
            }, 3000);
        }
    }

    handleAuthQR(data) {
        console.log('🔐 Processing auth QR:', data);

        // Envoyer les données QR au serveur pour authentification
        this.processAuthQR(data);
    }

    handleAuthURLQR(data) {
        console.log('🔗 Processing auth URL QR:', data);

        // Extraire les paramètres de l'URL
        const urlParts = data.split('/auth/qr-login/');
        if (urlParts.length === 2) {
            const authData = urlParts[1];
            const [userId, token] = authData.split(':');

            if (userId && token) {
                // Créer le format attendu par l'API
                const qrData = `LOCASPACE_AUTH:${userId}:${token}`;
                this.processAuthQR(qrData);
            } else {
                this.showError('Format d\'URL d\'authentification invalide');
            }
        } else {
            this.showError('URL d\'authentification invalide');
        }
    }

    async processAuthQR(qrData) {
        try {
            console.log('🚀 Sending auth QR to server:', qrData);

            // Afficher un indicateur de chargement
            document.getElementById('qr-result-text').innerHTML =
                '<i class="fas fa-spinner fa-spin me-2"></i>Connexion en cours...';

            // Envoyer au serveur
            const response = await fetch('/api/qr/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    qr_data: qrData
                })
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ QR authentication successful');

                // Afficher le succès
                document.getElementById('qr-result-text').innerHTML =
                    '<i class="fas fa-check-circle me-2 text-success"></i>Connexion réussie !';

                // Rediriger vers le dashboard
                setTimeout(() => {
                    window.location.href = result.redirect || '/dashboard';
                }, 1500);

            } else {
                console.error('❌ QR authentication failed:', result.error);
                this.showError(result.error || 'Erreur d\'authentification');
            }

        } catch (error) {
            console.error('❌ Error processing auth QR:', error);
            this.showError('Erreur de connexion au serveur');
        }
    }

    handleReservationQR(data) {
        // Extraire l'ID de réservation
        const reservationId = data.replace('LOCASPACE_RESERVATION:', '');

        // Rediriger vers la réservation
        window.location.href = `/reservations/${reservationId}`;
    }

    handleURLQR(data) {
        // Ouvrir l'URL
        if (confirm('Ouvrir ce lien ?\n' + data)) {
            window.open(data, '_blank');
        }
    }

    handleGenericQR(data) {
        // Afficher le contenu générique
        alert('QR Code scanné :\n' + data);
    }

    stopScanning() {
        console.log('🛑 Stopping QR scan...');

        this.isScanning = false;

        // Arrêter le stream vidéo
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }

        // Fermer l'interface
        const modal = document.getElementById('qr-scanner-modal');
        if (modal) {
            modal.remove();
        }
    }

    async switchCamera() {
        try {
            // Arrêter le stream actuel
            if (this.stream) {
                this.stream.getTracks().forEach(track => track.stop());
            }

            // Basculer entre caméra avant et arrière
            const currentFacing = this.stream?.getVideoTracks()[0]?.getSettings()?.facingMode || 'environment';
            const newFacing = currentFacing === 'environment' ? 'user' : 'environment';

            this.stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: newFacing,
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                }
            });

            this.videoElement.srcObject = this.stream;
            this.videoElement.play();

        } catch (error) {
            console.error('❌ Error switching camera:', error);
            this.showError('Impossible de changer de caméra');
        }
    }

    generateQRCode(data) {
        console.log('🎨 Generating QR code for:', data);

        // Créer l'interface de génération
        this.createGenerateInterface(data);
    }

    createGenerateInterface(data) {
        const modalHTML = `
            <div id="qr-generate-modal" class="qr-scanner-modal">
                <div class="qr-scanner-content">
                    <div class="qr-scanner-header">
                        <h3><i class="fas fa-qrcode me-2"></i>QR Code généré</h3>
                        <button class="qr-close-btn" onclick="document.getElementById('qr-generate-modal').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="qr-scanner-body text-center">
                        <div id="qr-code-container" class="qr-code-container">
                            <!-- QR code sera généré ici -->
                        </div>

                        <div class="qr-info mt-3">
                            <p class="text-muted">Scannez ce code avec votre téléphone</p>
                            <small class="text-muted">${data}</small>
                        </div>
                    </div>

                    <div class="qr-scanner-footer">
                        <button class="btn btn-secondary" onclick="document.getElementById('qr-generate-modal').remove()">
                            <i class="fas fa-times me-2"></i>Fermer
                        </button>
                        <button class="btn btn-primary" onclick="qrScanner.downloadQR()">
                            <i class="fas fa-download me-2"></i>Télécharger
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Générer le QR code avec QRCode.js
        new QRCode(document.getElementById('qr-code-container'), {
            text: data,
            width: 256,
            height: 256,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
        });

        document.getElementById('qr-generate-modal').style.display = 'block';
    }

    downloadQR() {
        const canvas = document.querySelector('#qr-code-container canvas');
        if (canvas) {
            const link = document.createElement('a');
            link.download = 'qrcode.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    }

    showError(message) {
        document.getElementById('qr-error-text').textContent = message;
        document.getElementById('qr-error').style.display = 'block';
        document.getElementById('scan-status').style.display = 'none';
    }
}

// Initialiser le scanner
let qrScanner;
document.addEventListener('DOMContentLoaded', function() {
    qrScanner = new QRCodeScanner();
    console.log('✅ QR Scanner ready');
});

// Fonctions globales
window.startQRScan = () => qrScanner.startScanning();
window.stopQRScan = () => qrScanner.stopScanning();
window.generateQR = (data) => qrScanner.generateQRCode(data);
