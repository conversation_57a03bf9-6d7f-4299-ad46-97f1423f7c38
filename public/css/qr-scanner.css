/* ===== QR SCANNER STYLES ===== */

/* Modal de base */
.qr-scanner-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

/* Camera Permission Overlay */
.camera-permission-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.permission-content {
    text-align: center;
    max-width: 500px;
    padding: 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(79, 70, 229, 0.1);
}

.permission-icon {
    margin-bottom: 1.5rem;
    animation: permissionPulse 2s ease-in-out infinite;
}

@keyframes permissionPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

.permission-content h4 {
    color: #1f2937;
    margin-bottom: 1rem;
    font-weight: 600;
}

.permission-content p {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.permission-steps {
    text-align: left;
    margin: 1.5rem 0;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 10px;
    border-left: 4px solid #4f46e5;
}

.step {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.step:last-child {
    margin-bottom: 0;
}

.step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: #4f46e5;
    color: white;
    border-radius: 50%;
    font-size: 0.75rem;
    font-weight: bold;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.permission-loading {
    color: #4f46e5;
    font-weight: 500;
    margin-top: 1rem;
    animation: loadingPulse 1.5s ease-in-out infinite;
}

@keyframes loadingPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.permission-help {
    text-align: left;
    margin: 1.5rem 0;
    padding: 1rem;
    background: #fef3cd;
    border-radius: 10px;
    border-left: 4px solid #f59e0b;
}

.permission-help h6 {
    color: #92400e;
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.permission-help ul {
    margin: 0;
    padding-left: 1rem;
    color: #78350f;
}

.permission-help li {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.permission-help strong {
    color: #92400e;
}

.permission-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    margin-top: 1.5rem;
}

.permission-actions .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.permission-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.qr-scanner-content {
    background: white;
    border-radius: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Header */
.qr-scanner-header {
    background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.qr-scanner-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.qr-close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.qr-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Body */
.qr-scanner-body {
    padding: 1.5rem;
}

/* Container caméra */
.camera-container {
    position: relative;
    background: #000;
    border-radius: 15px;
    overflow: hidden;
    aspect-ratio: 4/3;
    margin-bottom: 1rem;
}

#qr-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 15px;
}

/* Overlay de scan */
.scan-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}

.scan-frame {
    width: 250px;
    height: 250px;
    position: relative;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(2px);
}

.scan-corners {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.corner {
    position: absolute;
    width: 30px;
    height: 30px;
    border: 3px solid #4f46e5;
    border-radius: 5px;
}

.corner.top-left {
    top: -3px;
    left: -3px;
    border-right: none;
    border-bottom: none;
    animation: cornerPulse 2s ease-in-out infinite;
}

.corner.top-right {
    top: -3px;
    right: -3px;
    border-left: none;
    border-bottom: none;
    animation: cornerPulse 2s ease-in-out infinite 0.5s;
}

.corner.bottom-left {
    bottom: -3px;
    left: -3px;
    border-right: none;
    border-top: none;
    animation: cornerPulse 2s ease-in-out infinite 1s;
}

.corner.bottom-right {
    bottom: -3px;
    right: -3px;
    border-left: none;
    border-top: none;
    animation: cornerPulse 2s ease-in-out infinite 1.5s;
}

@keyframes cornerPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* Instructions */
.scan-instructions {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: white;
    background: rgba(0, 0, 0, 0.7);
    padding: 1rem 1.5rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.scan-instructions p {
    margin: 0 0 0.5rem 0;
    font-weight: 500;
}

.scan-status {
    font-size: 0.875rem;
    opacity: 0.9;
    animation: statusBlink 1.5s ease-in-out infinite;
}

@keyframes statusBlink {
    0%, 100% { opacity: 0.9; }
    50% { opacity: 0.6; }
}

/* Résultats et erreurs */
.qr-result, .qr-error {
    margin-top: 1rem;
    animation: resultSlideIn 0.3s ease-out;
}

@keyframes resultSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Footer */
.qr-scanner-footer {
    background: #f8f9fa;
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    justify-content: space-between;
}

.qr-scanner-footer .btn {
    flex: 1;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.qr-scanner-footer .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* QR Code Generator */
.qr-code-container {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    border: 2px dashed #e2e8f0;
    display: inline-block;
    margin: 1rem auto;
}

.qr-code-container canvas,
.qr-code-container img {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.qr-info {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    border-left: 4px solid #4f46e5;
}

/* Boutons QR dans l'interface */
.qr-scan-btn {
    background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.qr-scan-btn:hover {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(79, 70, 229, 0.3);
}

.qr-generate-btn {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.qr-generate-btn:hover {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(245, 158, 11, 0.3);
}

/* QR Code dans le profil */
.profile-qr-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    border: 2px dashed #cbd5e1;
    margin: 2rem 0;
}

.profile-qr-code {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin: 1rem 0;
}

/* Responsive */
@media (max-width: 768px) {
    .qr-scanner-content {
        width: 95%;
        margin: 1rem;
    }

    .qr-scanner-header,
    .qr-scanner-body,
    .qr-scanner-footer {
        padding: 1rem;
    }

    .scan-frame {
        width: 200px;
        height: 200px;
    }

    .corner {
        width: 25px;
        height: 25px;
    }

    .scan-instructions {
        bottom: 10px;
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }

    .qr-scanner-footer {
        flex-direction: column;
    }

    .qr-scanner-footer .btn {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 480px) {
    .scan-frame {
        width: 150px;
        height: 150px;
    }

    .qr-code-container {
        padding: 1rem;
    }

    .profile-qr-section {
        padding: 1rem;
        margin: 1rem 0;
    }
}

/* Animations de chargement */
.qr-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: qrSpin 1s ease-in-out infinite;
}

@keyframes qrSpin {
    to { transform: rotate(360deg); }
}

/* États de succès et erreur */
.qr-success {
    color: #10b981;
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    animation: successPulse 0.5s ease-out;
}

@keyframes successPulse {
    0% { transform: scale(0.9); opacity: 0; }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); opacity: 1; }
}

.qr-error-state {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
}

/* Permissions caméra */
.camera-permission {
    text-align: center;
    padding: 2rem;
    color: #64748b;
}

.camera-permission i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .qr-scanner-content {
        background: #1e293b;
        color: white;
    }

    .qr-scanner-footer {
        background: #334155;
    }

    .qr-info {
        background: #334155;
        color: white;
    }
}
