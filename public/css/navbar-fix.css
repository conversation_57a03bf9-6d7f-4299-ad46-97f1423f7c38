/* ===== NAVBAR LINKS FIX ===== */

/* Force navbar links to be visible */
.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    display: flex !important;
    align-items: center !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    padding: 0.75rem 1rem !important;
    border-radius: 8px !important;
    margin: 0 0.25rem !important;
    transition: all 0.3s ease !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 1) !important;
    background-color: rgba(255, 255, 255, 0.15) !important;
    transform: translateY(-2px) !important;
}

.navbar-nav .nav-link.active {
    color: rgba(255, 255, 255, 1) !important;
    background-color: rgba(255, 255, 255, 0.25) !important;
}

.navbar-nav .nav-link span,
.navbar-nav .nav-link i {
    color: inherit !important;
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Force navbar structure */
.navbar-nav {
    display: flex !important;
    list-style: none !important;
    margin: 0 !important;
    padding: 0 !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.navbar-nav .nav-item {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Mobile responsive */
@media (max-width: 991.98px) {
    .navbar-collapse.show .navbar-nav {
        display: flex !important;
        flex-direction: column !important;
        width: 100% !important;
        padding: 1rem 0 !important;
    }
    
    .navbar-collapse.show .navbar-nav .nav-item {
        display: block !important;
        width: 100% !important;
    }
    
    .navbar-collapse.show .navbar-nav .nav-link {
        display: flex !important;
        width: 100% !important;
        padding: 1rem 1.5rem !important;
        justify-content: center !important;
        text-align: center !important;
    }
}

/* Ensure no conflicting styles hide the links */
.navbar-nav .nav-link {
    background-image: none !important;
    text-shadow: none !important;
    box-shadow: none !important;
}

/* Force button styles */
.btn-outline-light {
    color: rgba(255, 255, 255, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    background-color: transparent !important;
}

.btn-outline-light:hover {
    color: #4f46e5 !important;
    background-color: rgba(255, 255, 255, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.9) !important;
}

/* Debug: Add temporary borders to see elements */
.debug .navbar-nav {
    border: 2px solid yellow !important;
}

.debug .navbar-nav .nav-item {
    border: 1px solid red !important;
}

.debug .navbar-nav .nav-link {
    border: 1px solid cyan !important;
    background-color: rgba(255, 0, 0, 0.1) !important;
}
