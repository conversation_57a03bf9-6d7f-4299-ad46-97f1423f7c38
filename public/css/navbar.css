/* ===== NAVBAR MODERNE LOCASPACE ===== */

/* Variables CSS */
:root {
    --primary-color: #4f46e5;
    --primary-dark: #3730a3;
    --primary-light: #6366f1;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --white: #ffffff;
    --navbar-height: 70px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* Gradient Background */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

/* Navbar Base */
.navbar {
    min-height: var(--navbar-height);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
    z-index: 1050;
}

.navbar.shadow-lg {
    box-shadow: var(--shadow);
}

/* Navbar Spacer */
.navbar-spacer {
    height: var(--navbar-height);
}

/* Brand Styling */
.navbar-brand {
    font-size: 1.8rem !important;
    font-weight: 700;
    letter-spacing: -0.5px;
    transition: var(--transition);
    text-decoration: none !important;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

/* Logo Placeholder */
.logo-container {
    position: relative;
}

.logo-placeholder {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--warning-color) 0%, #ff8c00 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.logo-placeholder::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: var(--transition);
}

.navbar-brand:hover .logo-placeholder {
    transform: rotate(5deg) scale(1.1);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

.navbar-brand:hover .logo-placeholder::before {
    animation: shimmer 0.6s ease-out;
}

.logo-letter {
    font-size: 1.5rem;
    font-weight: 900;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1;
}

.brand-text {
    background: linear-gradient(45deg, #fff, var(--warning-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    letter-spacing: -1px;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Navigation Links */
.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: 8px;
    margin: 0 0.25rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    z-index: 10;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover::before {
    left: 100%;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.15) !important;
    transform: translateY(-2px);
    color: rgba(255, 255, 255, 1) !important;
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.25) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    color: rgba(255, 255, 255, 1) !important;
}

.navbar-nav .nav-link span {
    position: relative;
    z-index: 2;
    color: inherit !important;
}

.navbar-nav .nav-link i {
    position: relative;
    z-index: 2;
    color: inherit !important;
}

/* Force visibility for all navbar elements */
.navbar-nav,
.navbar-nav .nav-item,
.navbar-nav .nav-link {
    visibility: visible !important;
    opacity: 1 !important;
    display: flex !important;
}

.navbar-collapse.show .navbar-nav,
.navbar-collapse.collapsing .navbar-nav {
    display: flex !important;
    flex-direction: column;
}

.navbar-collapse.show .navbar-nav .nav-item,
.navbar-collapse.collapsing .navbar-nav .nav-item {
    display: block !important;
}

/* Toggler Button */
.navbar-toggler {
    border: none !important;
    padding: 0.5rem;
    border-radius: 8px;
    transition: var(--transition);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.navbar-toggler:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Dropdown Menus */
.dropdown-menu-modern {
    border: none;
    border-radius: 12px;
    box-shadow: var(--shadow-hover);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    background: var(--white);
    backdrop-filter: blur(10px);
    animation: dropdownFadeIn 0.3s ease-out;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: var(--transition);
    border-radius: 8px;
    margin: 0 0.5rem;
}

.dropdown-item:hover {
    background-color: var(--light-color);
    transform: translateX(5px);
}

.dropdown-header {
    font-weight: 600;
    color: var(--secondary-color);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0.75rem 1.5rem 0.5rem;
}

.dropdown-divider {
    margin: 0.5rem 1rem;
    opacity: 0.1;
}

/* User Avatar */
.user-avatar {
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, var(--warning-color), #ff6b35);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 0.9rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.user-avatar-large {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--warning-color), #ff6b35);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
    margin-right: 1rem;
}

.user-info {
    text-align: left;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-badge {
    background: var(--danger-color);
    color: var(--white);
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    margin-left: 0.5rem;
    font-weight: 600;
}

.user-header {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, var(--light-color), #e9ecef);
    margin: 0 0.5rem;
    border-radius: 8px;
}

.user-details h6 {
    font-weight: 600;
    color: var(--dark-color);
}

/* Notifications */
.notification-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.notification-dropdown {
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--dark-color);
}

.notification-text {
    font-size: 0.8rem;
    color: var(--secondary-color);
    margin-bottom: 0.25rem;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .navbar-nav {
        padding: 1rem 0;
    }

    .navbar-nav .nav-link {
        padding: 1rem 1.5rem !important;
        margin: 0.25rem 0;
        text-align: center;
    }

    .dropdown-menu-modern {
        position: static !important;
        transform: none !important;
        box-shadow: none;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        margin: 0.5rem 1rem;
        background: rgba(255, 255, 255, 0.95);
    }

    .notification-dropdown {
        width: 100%;
    }

    .user-info {
        text-align: center;
    }

    .navbar-spacer {
        height: auto;
    }

    .navbar {
        position: relative !important;
    }
}

@media (max-width: 575.98px) {
    .navbar-brand {
        font-size: 1.5rem !important;
    }

    .notification-dropdown {
        width: 280px;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus States */
.nav-link:focus,
.dropdown-item:focus,
.btn:focus {
    outline: 2px solid var(--warning-color);
    outline-offset: 2px;
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
