<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use App\Models\Reservation;
use App\Models\Invoice;

class NotificationService
{
    /**
     * Create a notification for a user.
     */
    public function createNotification(User $user, string $content, string $type = 'info'): Notification
    {
        return Notification::create([
            'user_id' => $user->id,
            'content' => $content,
            'type' => $type,
            'is_read' => false
        ]);
    }

    /**
     * Notify user when reservation is created.
     */
    public function notifyReservationCreated(Reservation $reservation): void
    {
        $content = "Votre réservation pour {$reservation->local->name} le {$reservation->date->format('d/m/Y')} a été créée avec succès.";
        
        $this->createNotification(
            $reservation->user,
            $content,
            'success'
        );
    }

    /**
     * Notify user when reservation is confirmed.
     */
    public function notifyReservationConfirmed(Reservation $reservation): void
    {
        $content = "Votre réservation pour {$reservation->local->name} le {$reservation->date->format('d/m/Y')} a été confirmée.";
        
        $this->createNotification(
            $reservation->user,
            $content,
            'success'
        );
    }

    /**
     * Notify user when reservation is cancelled.
     */
    public function notifyReservationCancelled(Reservation $reservation): void
    {
        $content = "Votre réservation pour {$reservation->local->name} le {$reservation->date->format('d/m/Y')} a été annulée.";
        
        $this->createNotification(
            $reservation->user,
            $content,
            'warning'
        );
    }

    /**
     * Notify user when payment is successful.
     */
    public function notifyPaymentSuccessful(Invoice $invoice): void
    {
        $content = "Votre paiement de {$invoice->amount}€ pour la facture #{$invoice->id} a été traité avec succès.";
        
        $this->createNotification(
            $invoice->reservation->user,
            $content,
            'payment'
        );
    }

    /**
     * Notify user when payment fails.
     */
    public function notifyPaymentFailed(Invoice $invoice): void
    {
        $content = "Le paiement de {$invoice->amount}€ pour la facture #{$invoice->id} a échoué. Veuillez réessayer.";
        
        $this->createNotification(
            $invoice->reservation->user,
            $content,
            'error'
        );
    }

    /**
     * Notify user about upcoming reservation.
     */
    public function notifyUpcomingReservation(Reservation $reservation): void
    {
        $content = "Rappel : Votre réservation pour {$reservation->local->name} commence dans 2 heures.";
        
        $this->createNotification(
            $reservation->user,
            $content,
            'warning'
        );
    }

    /**
     * Notify all users about system maintenance.
     */
    public function notifySystemMaintenance(string $maintenanceDate): void
    {
        $content = "Maintenance programmée le {$maintenanceDate}. Le service pourra être temporairement indisponible.";
        
        User::chunk(100, function ($users) use ($content) {
            foreach ($users as $user) {
                $this->createNotification($user, $content, 'admin');
            }
        });
    }

    /**
     * Notify user about new features.
     */
    public function notifyNewFeature(User $user, string $featureDescription): void
    {
        $content = "Nouvelle fonctionnalité disponible : {$featureDescription}";
        
        $this->createNotification($user, $content, 'info');
    }

    /**
     * Notify admins about new reservation.
     */
    public function notifyAdminsNewReservation(Reservation $reservation): void
    {
        $content = "Nouvelle réservation créée par {$reservation->user->name} pour {$reservation->local->name}.";
        
        User::where('role', 'admin')->chunk(50, function ($admins) use ($content) {
            foreach ($admins as $admin) {
                $this->createNotification($admin, $content, 'admin');
            }
        });
    }

    /**
     * Mark all notifications as read for a user.
     */
    public function markAllAsReadForUser(User $user): void
    {
        $user->notifications()->unread()->update(['is_read' => true]);
    }

    /**
     * Get unread notifications count for a user.
     */
    public function getUnreadCountForUser(User $user): int
    {
        return $user->notifications()->unread()->count();
    }

    /**
     * Clean old read notifications (older than 30 days).
     */
    public function cleanOldNotifications(): void
    {
        Notification::where('is_read', true)
            ->where('created_at', '<', now()->subDays(30))
            ->delete();
    }
}
