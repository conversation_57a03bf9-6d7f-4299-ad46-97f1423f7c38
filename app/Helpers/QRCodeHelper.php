<?php

namespace App\Helpers;

class QRCodeHelper
{
    /**
     * G<PERSON>érer un QR code avec les paramètres par défaut de LocaSpace
     */
    public static function generate(string $data, int $size = 256, int $margin = 10): string
    {
        try {
            // Utiliser une API QR simple avec Google Charts (fallback)
            $url = 'https://api.qrserver.com/v1/create-qr-code/';
            $params = http_build_query([
                'size' => $size . 'x' . $size,
                'data' => $data,
                'format' => 'png'
            ]);

            $qrUrl = $url . '?' . $params;

            // Récupérer l'image
            $imageData = file_get_contents($qrUrl);

            if ($imageData === false) {
                throw new \Exception('Impossible de générer le QR code');
            }

            return 'data:image/png;base64,' . base64_encode($imageData);

        } catch (\Exception $e) {
            // Fallback : générer un QR code simple avec du texte
            return self::generateFallbackQR($data);
        }
    }

    /**
     * Générer un QR code de fallback simple
     */
    private static function generateFallbackQR(string $data): string
    {
        // Créer une image simple avec le texte
        $width = 256;
        $height = 256;

        $image = imagecreate($width, $height);
        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);

        // Remplir le fond en blanc
        imagefill($image, 0, 0, $white);

        // Ajouter le texte
        $text = "QR: " . substr($data, 0, 20) . "...";
        imagestring($image, 3, 10, $height/2 - 10, $text, $black);

        // Capturer l'image
        ob_start();
        imagepng($image);
        $imageData = ob_get_contents();
        ob_end_clean();

        imagedestroy($image);

        return 'data:image/png;base64,' . base64_encode($imageData);
    }

    /**
     * Générer un QR code d'authentification pour un utilisateur
     */
    public static function generateAuthQR(int $userId, string $token): string
    {
        $data = "LOCASPACE_AUTH:{$userId}:{$token}";
        return self::generate($data);
    }

    /**
     * Générer un QR code pour une réservation
     */
    public static function generateReservationQR(int $reservationId): string
    {
        $data = "LOCASPACE_RESERVATION:{$reservationId}";
        return self::generate($data);
    }

    /**
     * Générer un QR code pour un local
     */
    public static function generateLocalQR(int $localId): string
    {
        $data = "LOCASPACE_LOCAL:{$localId}";
        return self::generate($data);
    }

    /**
     * Générer un QR code pour une URL
     */
    public static function generateUrlQR(string $url): string
    {
        return self::generate($url);
    }

    /**
     * Valider le format d'un QR code LocaSpace
     */
    public static function validateQRData(string $qrData): array
    {
        // QR code d'authentification
        if (preg_match('/^LOCASPACE_AUTH:(\d+):(.+)$/', $qrData, $matches)) {
            return [
                'type' => 'auth',
                'user_id' => (int) $matches[1],
                'token' => $matches[2],
                'valid' => true
            ];
        }

        // QR code de réservation
        if (preg_match('/^LOCASPACE_RESERVATION:(\d+)$/', $qrData, $matches)) {
            return [
                'type' => 'reservation',
                'reservation_id' => (int) $matches[1],
                'valid' => true
            ];
        }

        // QR code de local
        if (preg_match('/^LOCASPACE_LOCAL:(\d+)$/', $qrData, $matches)) {
            return [
                'type' => 'local',
                'local_id' => (int) $matches[1],
                'valid' => true
            ];
        }

        // URL générique
        if (filter_var($qrData, FILTER_VALIDATE_URL)) {
            return [
                'type' => 'url',
                'url' => $qrData,
                'valid' => true
            ];
        }

        // Format non reconnu
        return [
            'type' => 'unknown',
            'data' => $qrData,
            'valid' => false
        ];
    }

    /**
     * Générer un token sécurisé pour l'authentification QR
     */
    public static function generateSecureToken(int $userId): string
    {
        return hash('sha256', $userId . time() . config('app.key'));
    }

    /**
     * Vérifier la validité d'un token QR
     */
    public static function validateToken(string $token, int $userId, int $maxAge = 3600): bool
    {
        // Dans un vrai système, vous stockeriez les tokens en base avec timestamp
        // Ici, c'est une validation basique
        return !empty($token) && strlen($token) === 64;
    }
}
