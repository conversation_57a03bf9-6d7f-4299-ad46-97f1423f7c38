<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Reservation extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'local_id',
        'date',
        'start_time',
        'end_time',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'date' => 'date',
            'start_time' => 'datetime:H:i',
            'end_time' => 'datetime:H:i',
        ];
    }

    /**
     * Get the user that owns the reservation.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the local that is reserved.
     */
    public function local(): BelongsTo
    {
        return $this->belongsTo(Local::class);
    }

    /**
     * Get the invoice for the reservation.
     */
    public function invoice(): HasOne
    {
        return $this->hasOne(Invoice::class);
    }

    /**
     * Calculate the duration in hours.
     */
    public function getDurationInHours(): float
    {
        $start = \Carbon\Carbon::parse($this->start_time);
        $end = \Carbon\Carbon::parse($this->end_time);
        return $end->diffInHours($start);
    }

    /**
     * Calculate the total amount for this reservation.
     */
    public function calculateAmount(): float
    {
        $duration = $this->getDurationInHours();
        $amount = $duration * abs($this->local->price);
        return abs($amount); // S'assurer que le montant est toujours positif
    }

    /**
     * Scope for confirmed reservations.
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmée');
    }

    /**
     * Scope for pending reservations.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'en attente');
    }

    /**
     * Scope for cancelled reservations.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'annulée');
    }

    /**
     * Scope for reservations on a specific date.
     */
    public function scopeOnDate($query, $date)
    {
        return $query->where('date', $date);
    }
}
