<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Reservation;
use App\Models\Local;
use App\Models\Invoice;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class ReservationController extends Controller
{
    /**
     * Display a listing of user's reservations.
     */
    public function index()
    {
        $reservations = Auth::user()->reservations()
            ->with(['local', 'invoice'])
            ->orderBy('date', 'desc')
            ->paginate(10);

        return view('reservations.index', compact('reservations'));
    }

    /**
     * Show the form for creating a new reservation.
     */
    public function create(Local $local)
    {
        return view('reservations.create', compact('local'));
    }

    /**
     * Store a newly created reservation in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'local_id' => 'required|exists:locals,id',
            'date' => 'required|date|after_or_equal:today',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $local = Local::findOrFail($request->local_id);

        // Check availability
        if (!$local->isAvailable($request->date, $request->start_time, $request->end_time)) {
            return back()->withErrors([
                'time' => 'Ce créneau n\'est pas disponible.',
            ])->withInput();
        }

        DB::beginTransaction();

        try {
            // Create reservation
            $reservation = Reservation::create([
                'user_id' => Auth::id(),
                'local_id' => $request->local_id,
                'date' => $request->date,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'status' => 'en attente',
            ]);

            // Calculate amount and create invoice
            $amount = $reservation->calculateAmount();
            $invoice = Invoice::create([
                'reservation_id' => $reservation->id,
                'amount' => $amount,
                'payment_status' => 'non réglé',
            ]);

            DB::commit();

            return redirect()->route('reservations.show', $reservation)
                ->with('success', 'Réservation créée avec succès. Veuillez procéder au paiement.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors([
                'error' => 'Une erreur est survenue lors de la création de la réservation.',
            ])->withInput();
        }
    }

    /**
     * Display the specified reservation.
     */
    public function show(Reservation $reservation)
    {
        // Check if user owns this reservation or is admin
        if ($reservation->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            abort(403);
        }

        $reservation->load(['local', 'invoice']);
        return view('reservations.show', compact('reservation'));
    }

    /**
     * Cancel a reservation.
     */
    public function cancel(Reservation $reservation)
    {
        // Check if user owns this reservation
        if ($reservation->user_id !== Auth::id()) {
            abort(403);
        }

        // Can only cancel pending or confirmed reservations
        if (!in_array($reservation->status, ['en attente', 'confirmée'])) {
            return back()->withErrors([
                'error' => 'Cette réservation ne peut pas être annulée.',
            ]);
        }

        $reservation->update(['status' => 'annulée']);

        return redirect()->route('reservations.index')
            ->with('success', 'Réservation annulée avec succès.');
    }

    /**
     * Confirm a reservation (admin only).
     */
    public function confirm(Reservation $reservation)
    {
        if (!Auth::user()->isAdmin()) {
            abort(403);
        }

        $reservation->update(['status' => 'confirmée']);

        return back()->with('success', 'Réservation confirmée avec succès.');
    }

    /**
     * Get calendar data for a specific local.
     */
    public function calendar(Request $request, Local $local)
    {
        $month = $request->get('month', now()->month);
        $year = $request->get('year', now()->year);

        $reservations = $local->reservations()
            ->whereYear('date', $year)
            ->whereMonth('date', $month)
            ->where('status', '!=', 'annulée')
            ->get(['date', 'start_time', 'end_time', 'status']);

        $events = $reservations->map(function ($reservation) {
            return [
                'title' => $reservation->status === 'confirmée' ? 'Réservé' : 'En attente',
                'start' => $reservation->date->format('Y-m-d') . 'T' . $reservation->start_time,
                'end' => $reservation->date->format('Y-m-d') . 'T' . $reservation->end_time,
                'color' => $reservation->status === 'confirmée' ? '#dc3545' : '#ffc107',
            ];
        });

        return response()->json($events);
    }
}
