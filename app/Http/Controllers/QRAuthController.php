<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Helpers\QRCodeHelper;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

class QRAuthController extends Controller
{
    /**
     * Afficher la page de scan QR pour connexion
     */
    public function showQRLogin()
    {
        return view('auth.qr-login');
    }

    /**
     * Générer un QR code pour l'utilisateur connecté
     */
    public function generateUserQR()
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Non authentifié'], 401);
        }

        $user = Auth::user();

        // Générer un token sécurisé
        $token = hash('sha256', $user->id . time() . config('app.key'));

        // Stocker le token en session pour validation
        session(['qr_auth_token' => $token, 'qr_user_id' => $user->id]);

        // Créer les données du QR code
        $qrData = "LOCASPACE_AUTH:{$user->id}:{$token}";

        // Générer le QR code avec API externe
        $qrImage = $this->generateQRCodeImage($qrData);

        return response()->json([
            'success' => true,
            'qr_data' => $qrData,
            'qr_image' => $qrImage,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email
            ]
        ]);
    }

    /**
     * Connexion via QR code scanné
     */
    public function loginWithQR(Request $request)
    {
        $request->validate([
            'qr_data' => 'required|string'
        ]);

        $qrData = $request->qr_data;

        // Vérifier le format du QR code
        if (!str_starts_with($qrData, 'LOCASPACE_AUTH:')) {
            return response()->json(['error' => 'QR code invalide'], 400);
        }

        // Extraire les données
        $parts = explode(':', str_replace('LOCASPACE_AUTH:', '', $qrData));

        if (count($parts) !== 2) {
            return response()->json(['error' => 'Format QR code invalide'], 400);
        }

        [$userId, $token] = $parts;

        // Vérifier que l'utilisateur existe
        $user = User::find($userId);
        if (!$user) {
            return response()->json(['error' => 'Utilisateur non trouvé'], 404);
        }

        // Vérifier le token (optionnel, pour plus de sécurité)
        // Dans un vrai système, vous stockeriez les tokens en base

        // Connecter l'utilisateur
        Auth::login($user);

        return response()->json([
            'success' => true,
            'message' => 'Connexion réussie',
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email
            ],
            'redirect' => route('dashboard')
        ]);
    }

    /**
     * Générer un QR code pour une réservation
     */
    public function generateReservationQR($reservationId)
    {
        // Vérifier que la réservation existe et appartient à l'utilisateur
        $reservation = Auth::user()->reservations()->find($reservationId);

        if (!$reservation) {
            return response()->json(['error' => 'Réservation non trouvée'], 404);
        }

        // Créer les données du QR code
        $qrData = "LOCASPACE_RESERVATION:{$reservationId}";

        // Générer le QR code
        $qrImage = $this->generateQRCodeImage($qrData);

        return response()->json([
            'success' => true,
            'qr_data' => $qrData,
            'qr_image' => $qrImage,
            'reservation' => [
                'id' => $reservation->id,
                'local_name' => $reservation->local->name,
                'date' => $reservation->date,
                'time_start' => $reservation->time_start,
                'time_end' => $reservation->time_end
            ]
        ]);
    }

    /**
     * Générer une image QR code avec API externe
     */
    private function generateQRCodeImage(string $data): string
    {
        try {
            // Utiliser l'API QR Server (gratuite et fiable)
            $url = 'https://api.qrserver.com/v1/create-qr-code/';
            $params = http_build_query([
                'size' => '256x256',
                'data' => $data,
                'format' => 'png',
                'ecc' => 'H', // High error correction
                'margin' => '10'
            ]);

            $qrUrl = $url . '?' . $params;

            // Récupérer l'image
            $imageData = file_get_contents($qrUrl);

            if ($imageData === false) {
                throw new \Exception('Impossible de générer le QR code');
            }

            return 'data:image/png;base64,' . base64_encode($imageData);
        } catch (\Exception $e) {
            // Fallback : générer un QR code simple avec du texte
            return $this->generateFallbackQR($data);
        }
    }

    /**
     * Générer un QR code de fallback simple
     */
    private function generateFallbackQR(string $data): string
    {
        // Créer une image simple avec le texte
        $width = 256;
        $height = 256;

        $image = imagecreate($width, $height);
        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);
        $blue = imagecolorallocate($image, 79, 70, 229); // Couleur LocaSpace

        // Remplir le fond en blanc
        imagefill($image, 0, 0, $white);

        // Ajouter un cadre
        imagerectangle($image, 10, 10, $width - 10, $height - 10, $blue);

        // Ajouter le titre
        imagestring($image, 4, 20, 30, "LocaSpace QR", $blue);

        // Ajouter les données (tronquées)
        $text = substr($data, 0, 30);
        imagestring($image, 2, 20, 60, $text, $black);

        // Ajouter des informations
        imagestring($image, 2, 20, 80, "Scannez avec l'app", $black);
        imagestring($image, 2, 20, 100, "LocaSpace", $black);

        // Ajouter un pattern simple (simulation QR)
        for ($i = 0; $i < 10; $i++) {
            for ($j = 0; $j < 10; $j++) {
                if (($i + $j) % 2 == 0) {
                    imagefilledrectangle(
                        $image,
                        150 + $i * 8,
                        150 + $j * 8,
                        150 + $i * 8 + 6,
                        150 + $j * 8 + 6,
                        $black
                    );
                }
            }
        }

        // Capturer l'image
        ob_start();
        imagepng($image);
        $imageData = ob_get_contents();
        ob_end_clean();

        imagedestroy($image);

        return 'data:image/png;base64,' . base64_encode($imageData);
    }

    /**
     * Gérer les URLs d'authentification QR
     */
    public function handleAuthURL($authData)
    {
        try {
            // Décoder les données d'authentification
            $parts = explode(':', $authData);

            if (count($parts) !== 2) {
                return redirect()->route('login')->with('error', 'URL d\'authentification invalide');
            }

            [$userId, $token] = $parts;

            // Vérifier que l'utilisateur existe
            $user = User::find($userId);
            if (!$user) {
                return redirect()->route('login')->with('error', 'Utilisateur non trouvé');
            }

            // Connecter l'utilisateur automatiquement
            Auth::login($user);

            return redirect()->route('dashboard')->with('success', 'Connexion QR réussie !');

        } catch (\Exception $e) {
            \Log::error('QR Auth URL Error: ' . $e->getMessage());
            return redirect()->route('login')->with('error', 'Erreur lors de l\'authentification QR');
        }
    }
}
