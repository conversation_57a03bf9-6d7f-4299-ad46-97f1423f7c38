<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Store a newly created user.
     */
    public function store(Request $request)
    {
        // Check if user is admin
        if (!Auth::user()->isAdmin()) {
            return redirect()->back()->with('error', 'Accès non autorisé');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'role' => 'required|in:client,admin'
        ]);

        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'email_verified_at' => now(), // Auto-verify admin created users
            ]);

            return redirect()->route('admin.users')
                ->with('success', "Utilisateur {$user->name} créé avec succès");

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Erreur lors de la création de l\'utilisateur: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user)
    {
        // Check if user is admin
        if (!Auth::user()->isAdmin()) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'role' => 'required|in:client,admin',
            'password' => 'nullable|string|min:8'
        ]);

        try {
            $updateData = [
                'name' => $request->name,
                'email' => $request->email,
                'role' => $request->role,
            ];

            // Only update password if provided
            if ($request->filled('password')) {
                $updateData['password'] = Hash::make($request->password);
            }

            $user->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Utilisateur mis à jour avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la mise à jour: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified user.
     */
    public function destroy(User $user)
    {
        // Check if user is admin
        if (!Auth::user()->isAdmin()) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        // Prevent admin from deleting themselves
        if ($user->id === Auth::id()) {
            return response()->json(['error' => 'Vous ne pouvez pas supprimer votre propre compte'], 400);
        }

        try {
            // Check if user has reservations
            if ($user->reservations()->count() > 0) {
                return response()->json([
                    'error' => 'Impossible de supprimer un utilisateur avec des réservations existantes'
                ], 400);
            }

            $userName = $user->name;
            $user->delete();

            return response()->json([
                'success' => true,
                'message' => "Utilisateur {$userName} supprimé avec succès"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la suppression: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user data for editing.
     */
    public function edit(User $user)
    {
        // Check if user is admin
        if (!Auth::user()->isAdmin()) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        return response()->json([
            'success' => true,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'created_at' => $user->created_at->format('d/m/Y'),
                'reservations_count' => $user->reservations()->count()
            ]
        ]);
    }

    /**
     * Toggle user status (activate/deactivate).
     */
    public function toggleStatus(User $user)
    {
        // Check if user is admin
        if (!Auth::user()->isAdmin()) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        // Prevent admin from deactivating themselves
        if ($user->id === Auth::id()) {
            return response()->json(['error' => 'Vous ne pouvez pas désactiver votre propre compte'], 400);
        }

        try {
            $user->update([
                'is_active' => !$user->is_active
            ]);

            $status = $user->is_active ? 'activé' : 'désactivé';

            return response()->json([
                'success' => true,
                'message' => "Utilisateur {$status} avec succès",
                'is_active' => $user->is_active
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la mise à jour: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reset user password.
     */
    public function resetPassword(User $user)
    {
        // Check if user is admin
        if (!Auth::user()->isAdmin()) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        try {
            $newPassword = 'password123'; // Default password
            $user->update([
                'password' => Hash::make($newPassword)
            ]);

            return response()->json([
                'success' => true,
                'message' => "Mot de passe réinitialisé. Nouveau mot de passe: {$newPassword}",
                'new_password' => $newPassword
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la réinitialisation: ' . $e->getMessage()
            ], 500);
        }
    }
}
