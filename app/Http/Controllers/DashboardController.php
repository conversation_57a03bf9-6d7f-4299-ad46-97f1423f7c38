<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Reservation;
use App\Models\Local;

class DashboardController extends Controller
{
    /**
     * Show the application dashboard.
     */
    public function index()
    {
        $user = Auth::user();

        // Get user's recent reservations
        $recentReservations = $user->reservations()
            ->with(['local', 'invoice'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get upcoming reservations
        $upcomingReservations = $user->reservations()
            ->with(['local'])
            ->where('date', '>=', now()->toDateString())
            ->where('status', '!=', 'annulée')
            ->orderBy('date', 'asc')
            ->limit(3)
            ->get();

        // Get statistics
        $stats = [
            'total_reservations' => $user->reservations()->count(),
            'confirmed_reservations' => $user->reservations()->confirmed()->count(),
            'pending_reservations' => $user->reservations()->pending()->count(),
            'cancelled_reservations' => $user->reservations()->cancelled()->count(),
        ];

        // Get available locals for quick booking
        $availableLocals = Local::active()
            ->limit(6)
            ->get();

        return view('dashboard', compact(
            'recentReservations',
            'upcomingReservations',
            'stats',
            'availableLocals'
        ));
    }
}
