<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Notification;

class NotificationController extends Controller
{
    /**
     * Get user notifications for navbar dropdown.
     */
    public function getNotifications()
    {
        $user = Auth::user();
        
        // Get recent unread notifications
        $notifications = $user->notifications()
            ->unread()
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
        
        // Get unread count
        $unreadCount = $user->notifications()->unread()->count();
        
        return response()->json([
            'notifications' => $notifications->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'content' => $notification->content,
                    'type' => $notification->type,
                    'created_at' => $notification->created_at->diffForHumans(),
                    'icon' => $this->getNotificationIcon($notification->type),
                    'color' => $this->getNotificationColor($notification->type)
                ];
            }),
            'unread_count' => $unreadCount
        ]);
    }
    
    /**
     * Mark notification as read.
     */
    public function markAsRead(Notification $notification)
    {
        // Check if user owns this notification
        if ($notification->user_id !== Auth::id()) {
            return response()->json(['error' => 'Non autorisé'], 403);
        }
        
        $notification->markAsRead();
        
        return response()->json(['success' => true]);
    }
    
    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead()
    {
        Auth::user()->notifications()->unread()->update(['is_read' => true]);
        
        return response()->json(['success' => true]);
    }
    
    /**
     * Create a new notification.
     */
    public function create(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'content' => 'required|string|max:255',
            'type' => 'required|in:success,info,warning,error'
        ]);
        
        $notification = Notification::create([
            'user_id' => $request->user_id,
            'content' => $request->content,
            'type' => $request->type
        ]);
        
        return response()->json([
            'success' => true,
            'notification' => $notification
        ]);
    }
    
    /**
     * Get notification icon based on type.
     */
    private function getNotificationIcon($type)
    {
        return match($type) {
            'success' => 'fas fa-check-circle',
            'info' => 'fas fa-info-circle',
            'warning' => 'fas fa-exclamation-triangle',
            'error' => 'fas fa-times-circle',
            'reservation' => 'fas fa-calendar-check',
            'payment' => 'fas fa-credit-card',
            'admin' => 'fas fa-cog',
            default => 'fas fa-bell'
        };
    }
    
    /**
     * Get notification color based on type.
     */
    private function getNotificationColor($type)
    {
        return match($type) {
            'success' => 'text-success',
            'info' => 'text-info',
            'warning' => 'text-warning',
            'error' => 'text-danger',
            'reservation' => 'text-primary',
            'payment' => 'text-success',
            'admin' => 'text-secondary',
            default => 'text-primary'
        };
    }
}
