# LocaSpace - Script de démarrage réseau
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    LocaSpace - Démarrage Réseau" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Obtenir l'adresse IP automatiquement
$IP = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "Wi-Fi" | Where-Object {$_.IPAddress -like "192.168.*" -or $_.IPAddress -like "10.*" -or $_.IPAddress -like "172.*"}).IPAddress

if (-not $IP) {
    # Fallback: essayer toutes les interfaces
    $IP = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -like "192.168.*" -or $_.IPAddress -like "10.*" -or $_.IPAddress -like "172.*"} | Select-Object -First 1).IPAddress
}

if ($IP) {
    Write-Host "Adresse IP détectée: $IP" -ForegroundColor Green
    
    # Mettre à jour le fichier .env
    $envContent = Get-Content .env
    $envContent = $envContent -replace 'APP_URL=.*', "APP_URL=http://$IP:8000"
    $envContent | Set-Content .env
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "  Application accessible sur le réseau:" -ForegroundColor Yellow
    Write-Host "  http://$IP:8000" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "Pages principales:" -ForegroundColor Yellow
    Write-Host "  - Accueil: http://$IP:8000" -ForegroundColor White
    Write-Host "  - QR Code: http://$IP:8000/qr-login" -ForegroundColor White
    Write-Host "  - Locaux: http://$IP:8000/locals" -ForegroundColor White
    Write-Host "  - Profil: http://$IP:8000/profile" -ForegroundColor White
    Write-Host ""
    
    Write-Host "Connexion test:" -ForegroundColor Yellow
    Write-Host "  Email: <EMAIL>" -ForegroundColor White
    Write-Host "  Mot de passe: password" -ForegroundColor White
    Write-Host ""
    
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "  Démarrage du serveur Laravel..." -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    # Générer un QR code pour l'URL (optionnel)
    Write-Host "QR Code pour accès mobile:" -ForegroundColor Yellow
    Write-Host "  Scannez ce QR code avec votre téléphone:" -ForegroundColor White
    Write-Host ""
    
    # Démarrer le serveur Laravel
    php artisan serve --host=0.0.0.0 --port=8000
    
} else {
    Write-Host "Erreur: Impossible de détecter l'adresse IP" -ForegroundColor Red
    Write-Host "Veuillez vérifier votre connexion réseau" -ForegroundColor Red
}

Read-Host "Appuyez sur Entrée pour fermer"
