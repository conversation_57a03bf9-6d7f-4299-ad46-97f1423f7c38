@props([
    'size' => 'md',
    'showText' => true,
    'class' => ''
])

@php
    $sizes = [
        'sm' => ['container' => '32px', 'letter' => '1rem'],
        'md' => ['container' => '45px', 'letter' => '1.5rem'],
        'lg' => ['container' => '60px', 'letter' => '2rem'],
        'xl' => ['container' => '80px', 'letter' => '2.5rem']
    ];
    
    $currentSize = $sizes[$size] ?? $sizes['md'];
@endphp

<div class="logo-component d-flex align-items-center {{ $class }}" {{ $attributes }}>
    <div class="logo-container me-2">
        <div class="logo-placeholder" 
             style="width: {{ $currentSize['container'] }}; height: {{ $currentSize['container'] }};">
            <span class="logo-letter" style="font-size: {{ $currentSize['letter'] }};">L</span>
        </div>
    </div>
    @if($showText)
        <span class="brand-text">LocaSpace</span>
    @endif
</div>

<style>
.logo-component .logo-container {
    position: relative;
}

.logo-component .logo-placeholder {
    background: linear-gradient(135deg, #f59e0b 0%, #ff8c00 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.logo-component .logo-placeholder::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.3s ease;
}

.logo-component:hover .logo-placeholder {
    transform: rotate(5deg) scale(1.1);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

.logo-component:hover .logo-placeholder::before {
    animation: logoShimmer 0.6s ease-out;
}

.logo-component .logo-letter {
    font-weight: 900;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1;
}

.logo-component .brand-text {
    background: linear-gradient(45deg, #1e293b, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    letter-spacing: -1px;
    font-size: 1.8rem;
}

@keyframes logoShimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .logo-component .brand-text {
        font-size: 1.5rem;
    }
}
</style>
