@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>Tableau de bord
                    </h1>
                    <p class="text-muted">Bienvenue, {{ Auth::user()->name }} !</p>
                </div>
                <div>
                    <a href="{{ route('locals.index') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Nouvelle réservation
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $stats['total_reservations'] }}</h4>
                            <p class="mb-0">Total réservations</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $stats['confirmed_reservations'] }}</h4>
                            <p class="mb-0">Confirmées</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $stats['pending_reservations'] }}</h4>
                            <p class="mb-0">En attente</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $stats['cancelled_reservations'] }}</h4>
                            <p class="mb-0">Annulées</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Upcoming Reservations -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check me-2"></i>Prochaines réservations
                    </h5>
                </div>
                <div class="card-body">
                    @if($upcomingReservations->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Local</th>
                                        <th>Date</th>
                                        <th>Heure</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($upcomingReservations as $reservation)
                                    <tr>
                                        <td>
                                            <strong>{{ $reservation->local->name }}</strong><br>
                                            <small class="text-muted">{{ $reservation->local->location }}</small>
                                        </td>
                                        <td>{{ $reservation->date->format('d/m/Y') }}</td>
                                        <td>{{ $reservation->start_time }} - {{ $reservation->end_time }}</td>
                                        <td>
                                            @if($reservation->status === 'confirmée')
                                                <span class="badge bg-success">Confirmée</span>
                                            @elseif($reservation->status === 'en attente')
                                                <span class="badge bg-warning">En attente</span>
                                            @else
                                                <span class="badge bg-danger">Annulée</span>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="{{ route('reservations.show', $reservation) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ route('reservations.index') }}" class="btn btn-outline-primary">
                                Voir toutes mes réservations
                            </a>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5>Aucune réservation à venir</h5>
                            <p class="text-muted">Commencez par réserver un local !</p>
                            <a href="{{ route('locals.index') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Faire une réservation
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Actions rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('locals.index') }}" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Parcourir les locaux
                        </a>
                        <a href="{{ route('reservations.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>Mes réservations
                        </a>
                        <a href="{{ route('profile.show') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-user me-2"></i>Mon profil
                        </a>
                    </div>
                </div>
            </div>

            <!-- Available Locals Preview -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Locaux populaires
                    </h5>
                </div>
                <div class="card-body">
                    @if($availableLocals->count() > 0)
                        @foreach($availableLocals->take(3) as $local)
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                @if($local->type === 'sport')
                                    <i class="fas fa-futbol text-success fa-2x"></i>
                                @elseif($local->type === 'conference')
                                    <i class="fas fa-presentation-screen text-primary fa-2x"></i>
                                @else
                                    <i class="fas fa-glass-cheers text-warning fa-2x"></i>
                                @endif
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-0">{{ $local->name }}</h6>
                                <small class="text-muted">{{ $local->location }}</small><br>
                                <small class="text-success fw-bold">{{ abs($local->price) }} MAD/h</small>
                            </div>
                            <div>
                                <a href="{{ route('locals.show', $local) }}" class="btn btn-sm btn-outline-primary">
                                    Voir
                                </a>
                            </div>
                        </div>
                        @endforeach
                        <div class="text-center">
                            <a href="{{ route('locals.index') }}" class="btn btn-sm btn-outline-primary">
                                Voir tous les locaux
                            </a>
                        </div>
                    @else
                        <p class="text-muted text-center">Aucun local disponible</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
