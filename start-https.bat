@echo off
echo ========================================
echo    LocaSpace - Demarrage HTTPS
echo ========================================
echo.

REM Obtenir l'adresse IP automatiquement
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set IP=%%a
    goto :found
)

:found
REM Nettoyer l'IP (supprimer les espaces)
set IP=%IP: =%

echo Adresse IP detectee: %IP%
echo.

REM Vérifier si OpenSSL est disponible
where openssl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  OpenSSL non trouve. Installation requise pour HTTPS.
    echo.
    echo Options:
    echo 1. Installer OpenSSL: https://slproweb.com/products/Win32OpenSSL.html
    echo 2. Utiliser un reverse proxy (nginx, Apache)
    echo 3. Deployer sur un serveur avec certificat SSL
    echo.
    echo Demarrage en HTTP pour le moment...
    goto :http_start
)

REM Créer un certificat auto-signé si il n'existe pas
if not exist "server.crt" (
    echo 🔐 Creation d'un certificat SSL auto-signe...
    
    openssl req -x509 -newkey rsa:4096 -keyout server.key -out server.crt -days 365 -nodes -subj "/C=FR/ST=France/L=Paris/O=LocaSpace/OU=Dev/CN=%IP%"
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ Certificat SSL cree avec succes
    ) else (
        echo ❌ Erreur lors de la creation du certificat
        goto :http_start
    )
)

echo.
echo ========================================
echo  Application HTTPS accessible sur:
echo  https://%IP%:8443
echo ========================================
echo.
echo ⚠️  IMPORTANT: Certificat auto-signe
echo    Votre navigateur affichera un avertissement
echo    Cliquez "Avance" puis "Continuer vers le site"
echo.
echo Pages principales:
echo  - Accueil: https://%IP%:8443
echo  - QR Code: https://%IP%:8443/qr-login
echo  - Test Camera: https://%IP%:8443/camera-test
echo  - Profil: https://%IP%:8443/profile
echo.
echo ========================================
echo  Demarrage du serveur HTTPS...
echo ========================================
echo.

REM Mettre à jour le fichier .env avec HTTPS
powershell -Command "(Get-Content .env) -replace 'APP_URL=.*', 'APP_URL=https://%IP%:8443' | Set-Content .env"

REM Démarrer avec HTTPS (nécessite un serveur web avec support SSL)
echo Note: Laravel serve ne supporte pas SSL nativement
echo Utilisation d'un serveur de developpement avec SSL...

REM Alternative: utiliser un proxy HTTPS
echo Demarrage du serveur Laravel...
start /B php artisan serve --host=0.0.0.0 --port=8000

echo.
echo 🔧 Configuration manuelle requise:
echo    1. Installer un reverse proxy (nginx, Apache)
echo    2. Configurer SSL avec les certificats generes
echo    3. Rediriger vers http://localhost:8000
echo.

goto :end

:http_start
echo ========================================
echo  Demarrage en HTTP (developpement)
echo  http://%IP%:8000
echo ========================================
echo.
echo ⚠️  ATTENTION: La camera peut ne pas fonctionner
echo    Les navigateurs modernes requierent HTTPS
echo    pour l'acces a la camera
echo.

REM Mettre à jour le fichier .env avec HTTP
powershell -Command "(Get-Content .env) -replace 'APP_URL=.*', 'APP_URL=http://%IP%:8000' | Set-Content .env"

REM Démarrer le serveur Laravel
php artisan serve --host=0.0.0.0 --port=8000

:end
pause
