# Configuration Nginx pour LocaSpace avec HTTPS
# Placez ce fichier dans /etc/nginx/sites-available/ (Linux) ou nginx/conf.d/ (Windows)

# Redirection HTTP vers HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name ************* localhost;
    
    # Redirection permanente vers HTTPS
    return 301 https://$server_name$request_uri;
}

# Configuration HTTPS
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name ************* localhost;
    
    # Certificats SSL
    ssl_certificate /path/to/locaspace/server.crt;
    ssl_certificate_key /path/to/locaspace/server.key;
    
    # Configuration SSL moderne
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Headers de sécurité
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Permissions pour caméra et microphone
    add_header Permissions-Policy "camera=(self), microphone=(self), geolocation=(self)" always;
    
    # Configuration Laravel
    root /path/to/locaspace/public;
    index index.php index.html index.htm;
    
    # Gestion des fichiers statiques
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # Gestion des routes Laravel
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # Configuration PHP-FPM
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;  # Ou unix:/var/run/php/php8.1-fpm.sock
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Timeout pour les uploads
        fastcgi_read_timeout 300;
        client_max_body_size 100M;
    }
    
    # Proxy vers Laravel serve (alternative pour développement)
    # Décommentez cette section si vous utilisez php artisan serve
    #
    # location / {
    #     proxy_pass http://127.0.0.1:8000;
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header X-Forwarded-Proto $scheme;
    #     proxy_set_header X-Forwarded-Host $server_name;
    #     proxy_redirect off;
    # }
    
    # Sécurité - Bloquer l'accès aux fichiers sensibles
    location ~ /\. {
        deny all;
    }
    
    location ~ /(storage|bootstrap/cache) {
        deny all;
    }
    
    location ~ /\.env {
        deny all;
    }
    
    # Logs
    access_log /var/log/nginx/locaspace_access.log;
    error_log /var/log/nginx/locaspace_error.log;
}

# Configuration pour WebSocket (si nécessaire pour temps réel)
# map $http_upgrade $connection_upgrade {
#     default upgrade;
#     '' close;
# }
# 
# server {
#     listen 6001 ssl;
#     server_name *************;
#     
#     ssl_certificate /path/to/locaspace/server.crt;
#     ssl_certificate_key /path/to/locaspace/server.key;
#     
#     location / {
#         proxy_pass http://127.0.0.1:6001;
#         proxy_http_version 1.1;
#         proxy_set_header Upgrade $http_upgrade;
#         proxy_set_header Connection $connection_upgrade;
#         proxy_set_header Host $host;
#         proxy_cache_bypass $http_upgrade;
#     }
# }
