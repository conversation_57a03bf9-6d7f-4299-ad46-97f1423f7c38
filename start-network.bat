@echo off
echo ========================================
echo    LocaSpace - Demarrage Reseau
echo ========================================
echo.

REM Obtenir l'adresse IP automatiquement
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set IP=%%a
    goto :found
)

:found
REM Nettoyer l'IP (supprimer les espaces)
set IP=%IP: =%

echo Adresse IP detectee: %IP%
echo.
echo Configuration de Laravel...

REM Mettre a jour le fichier .env avec la nouvelle IP
powershell -Command "(Get-Content .env) -replace 'APP_URL=.*', 'APP_URL=http://%IP%:8000' | Set-Content .env"

echo.
echo ========================================
echo  Application accessible sur le reseau:
echo  http://%IP%:8000
echo ========================================
echo.
echo Pages principales:
echo  - Accueil: http://%IP%:8000
echo  - QR Code: http://%IP%:8000/qr-login
echo  - Locaux: http://%IP%:8000/locals
echo  - Profil: http://%IP%:8000/profile
echo.
echo Connexion test:
echo  Email: <EMAIL>
echo  Mot de passe: password
echo.
echo ========================================
echo  Demarrage du serveur Laravel...
echo ========================================
echo.

REM Demarrer le serveur Laravel
php artisan serve --host=0.0.0.0 --port=8000

pause
